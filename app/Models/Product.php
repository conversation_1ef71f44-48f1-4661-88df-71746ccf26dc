<?php

namespace App\Models;

use App\Traits\Auditable;
use App\Traits\MultiTenantModelTrait;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Product extends Model implements HasMedia
{
    use SoftDeletes, MultiTenantModelTrait, InteractsWithMedia, Auditable, HasFactory;

    public $table = 'products';

    protected $appends = [
        'product_image',
    ];

    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public const DUTIES_TYPE_RADIO = [
        'percentage' => '%',
        'number'     => 'Num',
    ];

    public const MARGIN_TYPE_RADIO = [
        'percentage' => '%',
        'number'     => 'Num',
    ];

    public const CHARGES_TYPE_RADIO = [
        'percentage' => '%',
        'number'     => 'Num',
    ];

    public const CLEARENCE_TYPE_RADIO = [
        'percentage' => '%',
        'number'     => 'Num',
    ];

    public const MARK_UP_PRICE_TYPE_RADIO = [
        'percentage' => '%',
        'number'     => 'Num',
    ];

    public const SHIPPING_CHARGES_TYPE_RADIO = [
        'percentage' => '%',
        'number'     => 'Num',
    ];

    public const DISCOUNT_REBATE_REFUND_TYPE_RADIO = [
        'percentage' => '%',
        'number'     => 'Num',
    ];

    public const VAT_STRATEGY_RADIO = [
        'vatable'    => 'Vatable',
        'exempted'   => 'Exempted',
        'zero_rated' => 'Zero Rated',
    ];
    public const ITEM_TYPE_RADIO = [
        'product'    => 'Product',
        'service'   => 'Service',

    ];

    public const ITEM_STATUS_SELECT = [
        '0'   => 'Comfirmed',
        '1'   => 'To process',
        '2'   => 'Plan To Validate',
    ];

    public const UNIT_OF_SALES = [
        'unit'   => 'Unit',
        'kg' => 'kilogram',
        'g' => 'gram',
        'l' => 'litre',
        'ml' => 'millilitre',
        'm' => 'metre',
        'cm' => 'centimetre',
        'mm' => 'millimetre',
        'ft' => 'foot',
        'in' => 'inch',
        'sqm' => 'square metre',
        'sqcm' => 'square centimetre',
        'sqmm' => 'square millimetre',
        'sqft' => 'square foot',
        'sqin' => 'square inch',
    ];

    protected $fillable = [
        'product_code',
        'name',
        'product_category_id',
        'product_sub_category_id',
        'product_sub_sub_category_id',
        'description',
        'brand',
        'min_restock_level',
        'unit_sales_price',
        'unit_purchase_price',
        'opening_stock_level',
        'available_stock_level',
        'vat_strategy',
        'created_at',
        'supplier_id',
        'country_id',
        'currency_id',
        'rate_of_exchange',
        'unit_buying_in_currency',
        'unit_buying_local_price',
        'charges',
        'charges_type',
        'shipping_charges',
        'shipping_charges_type',
        'clearence',
        'clearence_type',
        'duties',
        'duties_type',
        'delivery_charges',
        'net_price',
        'discount_rebate_refund',
        'discount_rebate_refund_type',
        'mark_up_price',
        'margin',
        'mark_up_price_type',
        'margin_type',
        'net_selling_price',
        'average_cost',
        'project_id',
        'updated_at',
        'deleted_at',
        'team_id',
        'comments',
        'unit_of_sales',
        'store_id',
        'link_a_supplier',
        'calculate_costing',
        'reorder',
        'item_type',
        'toggle_supplier_item',
        'toggle_costing_item',
        'apply_rate_of_exchange',
        'shipping_charges_toggle',
        'new_sale_price',
        'final_sale_price',
        'pack_size',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    public function productCodeInvoiceItems()
    {
        return $this->hasMany(InvoiceItem::class, 'product_code_id', 'id');
    }

    public function productCodeQuotationItems()
    {
        return $this->hasMany(QuotationItem::class, 'product_code_id', 'id');
    }

    // public function productCodeDraftQuotationItems()
    // {
    //     return $this->hasMany(DraftQuotationItem::class, 'product_code_id', 'id');
    // }

    public function product_category()
    {
        return $this->belongsTo(ProductCategory::class, 'product_category_id');
    }

    public function product_sub_category()
    {
        return $this->belongsTo(ProductCategory::class, 'product_sub_category_id');
    }

    public function product_sub_sub_category()
    {
        return $this->belongsTo(ProductCategory::class, 'product_sub_sub_category_id');
    }

    public function product_tags()
    {
        return $this->belongsToMany(ProductTag::class);
    }

    public function supplier()
    {
        return $this->belongsTo(Supplier::class, 'supplier_id');
    }

    public function country()
    {
        return $this->belongsTo(Country::class, 'country_id');
    }

    public function currency()
    {
        return $this->belongsTo(Country::class, 'currency_id');
    }

    public function team()
    {
        return $this->belongsTo(Team::class, 'team_id');
    }

    public function store()
    {
        return $this->belongsTo(Store::class, 'store_id');
    }

    public function project()
    {
        return $this->belongsTo(Project::class, 'project_id');
    }

    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')->fit('crop', 50, 50);
        $this->addMediaConversion('preview')->fit('crop', 120, 120);
    }

    public function getProductImageAttribute()
    {
        $file = $this->getMedia('product_image')->last();
        if ($file) {
            $file->url       = $file->getUrl();
            $file->thumbnail = $file->getUrl('thumb');
            $file->preview   = $file->getUrl('preview');
        }

        return $file;
    }


}
